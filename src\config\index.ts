import { config } from 'dotenv';
import { LLMConfig, BrowserProfile, AgentSettings } from '../types';

// Load environment variables
config();

export class Config {
  // LLM Configuration
  static getLLMConfig(): LLMConfig {
    const openaiKey = process.env.OPENAI_API_KEY;
    const anthropicKey = process.env.ANTHROPIC_API_KEY;
    const googleKey = process.env.GOOGLE_API_KEY;

    // Auto-detect provider based on available API keys
    if (openaiKey) {
      return {
        provider: 'openai',
        model: process.env.OPENAI_MODEL || 'gpt-4o',
        apiKey: openaiKey,
        baseURL: process.env.OPENAI_BASE_URL || 'https://api.openai.com/v1',
        temperature: parseFloat(process.env.LLM_TEMPERATURE || '0'),
        maxTokens: parseInt(process.env.LLM_MAX_TOKENS || '4000'),
      };
    } else if (anthropicKey) {
      return {
        provider: 'anthropic',
        model: process.env.ANTHROPIC_MODEL || 'claude-3-5-sonnet-20241022',
        apiKey: anthropicKey,
        temperature: parseFloat(process.env.LLM_TEMPERATURE || '0'),
        maxTokens: parseInt(process.env.LLM_MAX_TOKENS || '4000'),
      };
    } else if (googleKey) {
      return {
        provider: 'google',
        model: process.env.GOOGLE_MODEL || 'gemini-1.5-flash',
        apiKey: googleKey,
        temperature: parseFloat(process.env.LLM_TEMPERATURE || '0'),
        maxTokens: parseInt(process.env.LLM_MAX_TOKENS || '4000'),
      };
    }

    throw new Error(
      'No LLM API key found. Please set OPENAI_API_KEY, ANTHROPIC_API_KEY, or GOOGLE_API_KEY'
    );
  }

  // Browser Configuration
  static getBrowserProfile(): BrowserProfile {
    const executablePath = process.env.BROWSER_EXECUTABLE_PATH;

    // Validate custom browser path if provided
    if (executablePath) {
      const fs = require('fs');
      if (!fs.existsSync(executablePath)) {
        console.warn(`Warning: Custom browser path not found: ${executablePath}`);
        console.warn('Falling back to default Playwright browser...');
      }
    }

    return {
      headless: process.env.BROWSER_HEADLESS === 'true',
      viewport: {
        width: parseInt(process.env.BROWSER_WIDTH || '1280'),
        height: parseInt(process.env.BROWSER_HEIGHT || '720'),
      },
      userDataDir: process.env.BROWSER_USER_DATA_DIR,
      executablePath: executablePath && require('fs').existsSync(executablePath) ? executablePath : undefined,
      timeout: parseInt(process.env.BROWSER_TIMEOUT || '30000'),
      autoInstall: process.env.BROWSER_AUTO_INSTALL !== 'false',
    };
  }

  // Agent Configuration
  static getAgentSettings(): AgentSettings {
    return {
      maxSteps: parseInt(process.env.AGENT_MAX_STEPS || '50'),
      maxActionsPerStep: parseInt(process.env.AGENT_MAX_ACTIONS_PER_STEP || '3'),
      useVision: process.env.AGENT_USE_VISION !== 'false',
      temperature: parseFloat(process.env.LLM_TEMPERATURE || '0'),
    };
  }

  // Logging Configuration
  static getLogLevel(): string {
    return process.env.LOG_LEVEL || 'info';
  }

  // Debug mode
  static isDebugMode(): boolean {
    return process.env.DEBUG === 'true';
  }
}

export default Config;
