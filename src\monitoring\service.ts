import { AgentStep, PerformanceMetrics, Action } from '../types';
import { logger } from '../utils/logger';
import { Helpers } from '../utils/helpers';

/**
 * 🌐 页面状态监控器 - 基于参考文件重构
 */
export interface PageState {
  url: string;
  title: string;
  domHash: string;
  timestamp: number;
  isLoading: boolean;
  hasNewContent: boolean;
  elementCount?: number;
  interactiveElementCount?: number;
}

export class PageStateMonitor {
  private page: any;
  private currentState: PageState;
  private stateHistory: PageState[] = [];
  private changeListeners: Array<(oldState: PageState, newState: PageState, eventType: string) => Promise<void>> = [];
  private monitoringActive = false;
  private lastDOMSnapshot: any = null;
  private domCheckInterval: any = null;

  constructor(page: any) {
    this.page = page;
    this.currentState = {
      url: '',
      title: '',
      domHash: '',
      timestamp: 0,
      isLoading: false,
      hasNewContent: false
    };
  }

  /**
   * 启动页面状态监控
   */
  async startMonitoring(): Promise<void> {
    if (this.monitoringActive) return;

    logger.info('🔄 启动页面状态监控器...', 'PageStateMonitor');

    try {
      // 监听页面导航事件
      this.page.on('domcontentloaded', () => this.handlePageChange('domcontentloaded'));
      this.page.on('load', () => this.handlePageChange('load'));
      this.page.on('framenavigated', () => this.handlePageChange('framenavigated'));

      // 定期检查DOM变化
      this.domCheckInterval = setInterval(() => {
        this.checkDOMChanges().catch(error => {
          logger.warn(`DOM变化检查失败: ${error.message}`, 'PageStateMonitor');
        });
      }, 2000);

      this.monitoringActive = true;

      // 获取初始状态
      await this.updateCurrentState();

      logger.info('✅ 页面状态监控器已启动', 'PageStateMonitor');
    } catch (error) {
      logger.error('❌ 页面状态监控器启动失败', error as Error, 'PageStateMonitor');
    }
  }

  /**
   * 处理页面变化事件
   */
  private async handlePageChange(eventType: string): Promise<void> {
    try {
      logger.info(`🔄 检测到页面事件: ${eventType}`, 'PageStateMonitor');

      // 等待页面稳定
      await this.waitForPageStability();

      // 更新状态
      const oldState = { ...this.currentState };
      await this.updateCurrentState();

      // 通知监听器
      await this.notifyStateChange(oldState, this.currentState, eventType);
    } catch (error) {
      logger.warn(`处理页面变化事件失败: ${error}`, 'PageStateMonitor');
    }
  }

  /**
   * 等待页面稳定
   */
  private async waitForPageStability(timeout = 10000): Promise<void> {
    try {
      logger.debug('⏳ 等待页面稳定...', 'PageStateMonitor');

      // 等待DOM加载完成
      await this.page.waitForLoadState('domcontentloaded', { timeout: 5000 });

      // 等待网络空闲
      await this.page.waitForLoadState('networkidle', { timeout: 5000 });

      // 额外等待确保动态内容加载
      await this.sleep(1500);

      logger.debug('✅ 页面已稳定', 'PageStateMonitor');
    } catch (error) {
      logger.warn(`⚠️ 等待页面稳定超时: ${error}`, 'PageStateMonitor');
    }
  }

  /**
   * 更新当前页面状态
   */
  private async updateCurrentState(): Promise<void> {
    try {
      const url = this.page.url();
      const title = await this.page.title();
      const domHash = await this.calculateDOMHash();
      const timestamp = Date.now();

      // 检查是否有新内容
      const hasNewContent = this.currentState.domHash !== '' &&
                           this.currentState.domHash !== domHash;

      // 获取元素统计
      const elementStats = await this.getElementStats();

      const oldState = { ...this.currentState };
      this.currentState = {
        url,
        title,
        domHash,
        timestamp,
        isLoading: false,
        hasNewContent,
        elementCount: elementStats.total,
        interactiveElementCount: elementStats.interactive
      };

      // 添加到历史记录
      this.stateHistory.push({ ...this.currentState });

      // 保持历史记录在合理范围内
      if (this.stateHistory.length > 50) {
        this.stateHistory = this.stateHistory.slice(-50);
      }

      logger.debug(`📊 页面状态已更新: ${url} (DOM: ${domHash.substring(0, 8)}...)`, 'PageStateMonitor');
    } catch (error) {
      logger.warn(`更新页面状态失败: ${error}`, 'PageStateMonitor');
    }
  }

  /**
   * 计算DOM哈希值
   */
  private async calculateDOMHash(): Promise<string> {
    try {
      return await this.page.evaluate(() => {
        // 获取页面的关键DOM结构信息
        const bodyHTML = document.body?.innerHTML || '';
        const title = document.title;
        const url = window.location.href;

        // 简化的哈希计算
        const content = `${url}|${title}|${bodyHTML.length}|${document.querySelectorAll('*').length}`;

        // 简单哈希函数
        let hash = 0;
        for (let i = 0; i < content.length; i++) {
          const char = content.charCodeAt(i);
          hash = ((hash << 5) - hash) + char;
          hash = hash & hash; // 转换为32位整数
        }

        return Math.abs(hash).toString(16);
      });
    } catch (error) {
      logger.warn(`计算DOM哈希失败: ${error}`, 'PageStateMonitor');
      return Date.now().toString();
    }
  }

  /**
   * 获取元素统计信息
   */
  private async getElementStats(): Promise<{ total: number; interactive: number }> {
    try {
      return await this.page.evaluate(() => {
        const allElements = document.querySelectorAll('*');
        const interactiveSelectors = [
          'button', 'a[href]', 'input', 'textarea', 'select',
          '[onclick]', '[role="button"]', '[role="link"]',
          '[tabindex]:not([tabindex="-1"])'
        ];
        const interactiveElements = document.querySelectorAll(interactiveSelectors.join(','));

        return {
          total: allElements.length,
          interactive: interactiveElements.length
        };
      });
    } catch (error) {
      logger.warn(`获取元素统计失败: ${error}`, 'PageStateMonitor');
      return { total: 0, interactive: 0 };
    }
  }

  /**
   * 检查DOM变化
   */
  private async checkDOMChanges(): Promise<void> {
    if (!this.monitoringActive) return;

    try {
      const currentDOMHash = await this.calculateDOMHash();

      if (this.currentState.domHash !== currentDOMHash) {
        logger.debug('🔄 检测到DOM变化', 'PageStateMonitor');

        const oldState = { ...this.currentState };
        await this.updateCurrentState();

        // 通知监听器
        await this.notifyStateChange(oldState, this.currentState, 'dom_change');
      }
    } catch (error) {
      logger.debug(`DOM变化检查失败: ${error}`, 'PageStateMonitor');
    }
  }

  /**
   * 通知状态变化监听器
   */
  private async notifyStateChange(oldState: PageState, newState: PageState, eventType: string): Promise<void> {
    for (const listener of this.changeListeners) {
      try {
        await listener(oldState, newState, eventType);
      } catch (error) {
        logger.warn(`状态变化监听器执行失败: ${error}`, 'PageStateMonitor');
      }
    }
  }

  /**
   * 添加状态变化监听器
   */
  addStateChangeListener(listener: (oldState: PageState, newState: PageState, eventType: string) => Promise<void>): void {
    this.changeListeners.push(listener);
    logger.debug('✅ 已添加页面状态变化监听器', 'PageStateMonitor');
  }

  /**
   * 获取当前页面状态
   */
  getCurrentState(): PageState {
    return { ...this.currentState };
  }

  /**
   * 获取状态历史
   */
  getStateHistory(): PageState[] {
    return [...this.stateHistory];
  }

  /**
   * 检查页面是否有新内容
   */
  hasNewContent(): boolean {
    return this.currentState.hasNewContent;
  }

  /**
   * 停止监控
   */
  stopMonitoring(): void {
    if (!this.monitoringActive) return;

    logger.info('🛑 停止页面状态监控器...', 'PageStateMonitor');

    this.monitoringActive = false;

    if (this.domCheckInterval) {
      clearInterval(this.domCheckInterval);
      this.domCheckInterval = null;
    }

    // 移除页面事件监听器
    try {
      this.page.removeAllListeners('domcontentloaded');
      this.page.removeAllListeners('load');
      this.page.removeAllListeners('framenavigated');
    } catch (error) {
      logger.debug(`移除页面监听器失败: ${error}`, 'PageStateMonitor');
    }

    logger.info('✅ 页面状态监控器已停止', 'PageStateMonitor');
  }

  /**
   * 辅助方法：睡眠
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

export interface PerformanceAlert {
  type: 'slow_action' | 'high_memory' | 'high_error_rate' | 'timeout_risk';
  severity: 'low' | 'medium' | 'high';
  message: string;
  timestamp: Date;
  metadata?: any;
}

export interface SystemMetrics {
  memoryUsage: {
    used: number;
    total: number;
    percentage: number;
  };
  cpuUsage?: number;
  networkLatency?: number;
  browserMemory?: number;
}

export class PerformanceMonitoringService {
  private startTime: Date = new Date();
  private actionTimes: Map<string, number[]> = new Map();
  private memorySnapshots: { timestamp: Date; usage: number }[] = [];
  private networkRequests: number = 0;
  private screenshotCount: number = 0;
  private errorCount: number = 0;
  private retryCount: number = 0;
  private alerts: PerformanceAlert[] = [];
  
  // Thresholds
  private readonly SLOW_ACTION_THRESHOLD = 10000; // 10 seconds
  private readonly HIGH_MEMORY_THRESHOLD = 0.8; // 80%
  private readonly HIGH_ERROR_RATE_THRESHOLD = 0.3; // 30%
  private readonly MAX_ALERTS = 100;

  startMonitoring(): void {
    this.startTime = new Date();
    this.recordMemorySnapshot();
    
    // Start periodic monitoring
    setInterval(() => {
      this.recordMemorySnapshot();
      this.checkPerformanceAlerts();
    }, 5000); // Every 5 seconds
    
    logger.info('Performance monitoring started', 'PerformanceMonitoringService');
  }

  recordActionStart(action: Action): string {
    const actionId = `${action.type}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    return actionId;
  }

  recordActionEnd(actionId: string, action: Action, success: boolean, duration: number): void {
    // Record timing
    const actionType = action.type;
    if (!this.actionTimes.has(actionType)) {
      this.actionTimes.set(actionType, []);
    }
    this.actionTimes.get(actionType)!.push(duration);

    // Update counters
    if (!success) {
      this.errorCount++;
    }

    // Check for slow actions
    if (duration > this.SLOW_ACTION_THRESHOLD) {
      this.addAlert({
        type: 'slow_action',
        severity: 'medium',
        message: `Slow ${actionType} action: ${duration}ms`,
        timestamp: new Date(),
        metadata: { action, duration },
      });
    }

    logger.debug(`Action ${actionType} completed in ${duration}ms`, 'PerformanceMonitoringService');
  }

  recordRetry(): void {
    this.retryCount++;
  }

  recordNetworkRequest(): void {
    this.networkRequests++;
  }

  recordScreenshot(): void {
    this.screenshotCount++;
  }

  recordError(error: Error, action?: Action): void {
    this.errorCount++;
    
    logger.debug(`Error recorded: ${error.message}`, 'PerformanceMonitoringService');
  }

  getMetrics(steps: AgentStep[]): PerformanceMetrics {
    const totalTime = Date.now() - this.startTime.getTime();
    const actionDurations = steps
      .map(s => s.result.metadata?.duration || 0)
      .filter(d => d > 0);

    const averageActionTime = actionDurations.length > 0 
      ? actionDurations.reduce((sum, d) => sum + d, 0) / actionDurations.length 
      : 0;

    const slowestAction = steps.reduce((slowest, step) => {
      const duration = step.result.metadata?.duration || 0;
      return duration > (slowest?.duration || 0) 
        ? { action: step.action, duration }
        : slowest;
    }, null as { action: Action; duration: number } | null);

    const fastestAction = steps.reduce((fastest, step) => {
      const duration = step.result.metadata?.duration || 0;
      return duration > 0 && duration < (fastest?.duration || Infinity)
        ? { action: step.action, duration }
        : fastest;
    }, null as { action: Action; duration: number } | null);

    const memoryUsage = this.getMemoryUsage();

    return {
      totalExecutionTime: totalTime,
      averageActionTime,
      slowestAction: slowestAction || { action: { type: 'done', message: '', success: true }, duration: 0 },
      fastestAction: fastestAction || { action: { type: 'done', message: '', success: true }, duration: 0 },
      memoryUsage: {
        initial: this.memorySnapshots[0]?.usage || 0,
        peak: Math.max(...this.memorySnapshots.map(s => s.usage), 0),
        final: this.memorySnapshots[this.memorySnapshots.length - 1]?.usage || 0,
      },
      networkRequests: this.networkRequests,
      screenshotsTaken: this.screenshotCount,
      errorsEncountered: this.errorCount,
      retriesPerformed: this.retryCount,
    };
  }

  getSystemMetrics(): SystemMetrics {
    const memUsage = process.memoryUsage();
    const totalMemory = require('os').totalmem();
    const usedMemory = memUsage.heapUsed + memUsage.external;

    return {
      memoryUsage: {
        used: usedMemory,
        total: totalMemory,
        percentage: usedMemory / totalMemory,
      },
    };
  }

  getActionStatistics(): Map<string, {
    count: number;
    averageTime: number;
    minTime: number;
    maxTime: number;
    successRate: number;
  }> {
    const stats = new Map();

    for (const [actionType, times] of this.actionTimes.entries()) {
      if (times.length > 0) {
        stats.set(actionType, {
          count: times.length,
          averageTime: times.reduce((sum, t) => sum + t, 0) / times.length,
          minTime: Math.min(...times),
          maxTime: Math.max(...times),
          successRate: 1.0, // Would need to track failures separately
        });
      }
    }

    return stats;
  }

  getAlerts(severity?: PerformanceAlert['severity']): PerformanceAlert[] {
    if (severity) {
      return this.alerts.filter(a => a.severity === severity);
    }
    return [...this.alerts];
  }

  clearAlerts(): void {
    this.alerts = [];
    logger.debug('Performance alerts cleared', 'PerformanceMonitoringService');
  }

  generateReport(steps: AgentStep[]): string {
    const metrics = this.getMetrics(steps);
    const systemMetrics = this.getSystemMetrics();
    const actionStats = this.getActionStatistics();
    const alerts = this.getAlerts();

    const report = [
      '=== PERFORMANCE REPORT ===',
      '',
      `Total Execution Time: ${Helpers.formatDuration(metrics.totalExecutionTime / 1000)}`,
      `Average Action Time: ${metrics.averageActionTime.toFixed(2)}ms`,
      `Network Requests: ${metrics.networkRequests}`,
      `Screenshots Taken: ${metrics.screenshotsTaken}`,
      `Errors Encountered: ${metrics.errorsEncountered}`,
      `Retries Performed: ${metrics.retriesPerformed}`,
      '',
      '--- Memory Usage ---',
      `Initial: ${(metrics.memoryUsage.initial / 1024 / 1024).toFixed(2)} MB`,
      `Peak: ${(metrics.memoryUsage.peak / 1024 / 1024).toFixed(2)} MB`,
      `Final: ${(metrics.memoryUsage.final / 1024 / 1024).toFixed(2)} MB`,
      `Current System: ${(systemMetrics.memoryUsage.percentage * 100).toFixed(1)}%`,
      '',
      '--- Action Statistics ---',
    ];

    for (const [actionType, stats] of actionStats.entries()) {
      report.push(`${actionType}: ${stats.count} times, avg ${stats.averageTime.toFixed(2)}ms`);
    }

    if (alerts.length > 0) {
      report.push('', '--- Performance Alerts ---');
      alerts.slice(-10).forEach(alert => {
        report.push(`[${alert.severity.toUpperCase()}] ${alert.message}`);
      });
    }

    return report.join('\n');
  }

  private recordMemorySnapshot(): void {
    const usage = process.memoryUsage().heapUsed;
    this.memorySnapshots.push({
      timestamp: new Date(),
      usage,
    });

    // Keep only last 100 snapshots
    if (this.memorySnapshots.length > 100) {
      this.memorySnapshots = this.memorySnapshots.slice(-100);
    }
  }

  private checkPerformanceAlerts(): void {
    // Check memory usage
    const systemMetrics = this.getSystemMetrics();
    if (systemMetrics.memoryUsage.percentage > this.HIGH_MEMORY_THRESHOLD) {
      this.addAlert({
        type: 'high_memory',
        severity: 'high',
        message: `High memory usage: ${(systemMetrics.memoryUsage.percentage * 100).toFixed(1)}%`,
        timestamp: new Date(),
        metadata: { memoryUsage: systemMetrics.memoryUsage },
      });
    }

    // Check error rate (if we have enough data)
    const totalActions = Array.from(this.actionTimes.values())
      .reduce((sum, times) => sum + times.length, 0);
    
    if (totalActions > 10) {
      const errorRate = this.errorCount / totalActions;
      if (errorRate > this.HIGH_ERROR_RATE_THRESHOLD) {
        this.addAlert({
          type: 'high_error_rate',
          severity: 'high',
          message: `High error rate: ${(errorRate * 100).toFixed(1)}%`,
          timestamp: new Date(),
          metadata: { errorRate, totalActions, errorCount: this.errorCount },
        });
      }
    }
  }

  private addAlert(alert: PerformanceAlert): void {
    this.alerts.push(alert);
    
    // Keep only recent alerts
    if (this.alerts.length > this.MAX_ALERTS) {
      this.alerts = this.alerts.slice(-this.MAX_ALERTS);
    }

    logger.warn(`Performance alert: ${alert.message}`, 'PerformanceMonitoringService');
  }

  private getMemoryUsage(): number {
    return process.memoryUsage().heapUsed;
  }
}
