const { chromium } = require('playwright');
import { BrowserProfile, DOMState } from '../types';
import { logger } from '../utils/logger';
import { DOMService } from '../dom/service';
import { Helpers } from '../utils/helpers';

// 浏览器会话管理类 - 负责浏览器的生命周期管理
export class BrowserSession {
  private browser: any = null;        // 浏览器实例
  private context: any = null;        // 浏览器上下文
  private page: any = null;           // 当前页面
  private domService: DOMService | null = null;  // DOM 服务
  private profile: BrowserProfile;               // 浏览器配置
  private tabs: any[] = [];                      // 标签页列表
  private currentTabIndex: number = 0;           // 当前标签页索引

  constructor(profile: BrowserProfile = {}) {
    this.profile = {
      headless: true,
      viewport: { width: 1280, height: 720 },
      timeout: 30000,
      ...profile,
    };
  }

  async start(): Promise<void> {
    try {
      logger.info('正在启动浏览器会话...', 'BrowserSession');

      // Try to launch browser, with auto-install fallback
      try {
        this.browser = await chromium.launch({
          headless: this.profile.headless,
          executablePath: this.profile.executablePath,
          timeout: this.profile.timeout,
          slowMo: this.profile.slowMo,
          devtools: this.profile.devtools,
          args: this.profile.args,
        });
      } catch (error: any) {
        // If browser not found and auto-install is enabled
        if (error.message.includes("Executable doesn't exist") && this.profile.autoInstall !== false) {
          logger.info('浏览器未找到，正在自动安装...', 'BrowserSession');

          try {
            // Install browser using playwright
            const { execSync } = require('child_process');
            execSync('npx playwright install chromium', { stdio: 'inherit' });

            logger.success('浏览器安装完成，重新启动...', 'BrowserSession');

            // Retry launch without custom executable path
            this.browser = await chromium.launch({
              headless: this.profile.headless,
              timeout: this.profile.timeout,
              slowMo: this.profile.slowMo,
              devtools: this.profile.devtools,
              args: this.profile.args,
            });
          } catch (installError) {
            logger.error('浏览器自动安装失败', installError as Error, 'BrowserSession');
            throw new Error(`浏览器启动失败。请手动运行: npx playwright install chromium`);
          }
        } else {
          throw error;
        }
      }

      // Create context
      this.context = await this.browser.newContext({
        viewport: this.profile.viewport,
        userAgent: this.profile.userAgent || 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        ignoreHTTPSErrors: this.profile.ignoreHTTPSErrors,
        proxy: this.profile.proxy,
        locale: this.profile.locale,
        timezoneId: this.profile.timezone,
        geolocation: this.profile.geolocation,
        permissions: this.profile.permissions,
        extraHTTPHeaders: this.profile.extraHTTPHeaders,
        colorScheme: this.profile.colorScheme,
        reducedMotion: this.profile.reducedMotion,
        forcedColors: this.profile.forcedColors,
      });

      // Create page
      this.page = await this.context.newPage();
      this.tabs = [this.page];

      // Initialize DOM service
      this.domService = new DOMService(this.page);

      // Set up event listeners
      this.setupEventListeners();

      logger.success('浏览器会话启动成功', 'BrowserSession');
    } catch (error) {
      logger.error('Failed to start browser session', error as Error, 'BrowserSession');
      throw error;
    }
  }

  async close(): Promise<void> {
    try {
      logger.info('Closing browser session...', 'BrowserSession');

      // Set a timeout for the entire close operation
      const closePromise = this.performClose();
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Browser close timeout')), 10000);
      });

      await Promise.race([closePromise, timeoutPromise]);
      logger.success('Browser session closed successfully', 'BrowserSession');
    } catch (error) {
      logger.error('Error closing browser session', error as Error, 'BrowserSession');
      // Force kill if normal close fails
      try {
        if (this.browser) {
          await this.browser.close();
        }
      } catch (killError) {
        logger.error('Failed to force close browser', killError as Error, 'BrowserSession');
      }
    }
  }

  private async performClose(): Promise<void> {
    if (this.page) {
      try {
        await this.page.close();
      } catch (error) {
        logger.warn('Failed to close page gracefully', 'BrowserSession');
      }
      this.page = null;
    }

    if (this.context) {
      try {
        await this.context.close();
      } catch (error) {
        logger.warn('Failed to close context gracefully', 'BrowserSession');
      }
      this.context = null;
    }

    if (this.browser) {
      try {
        await this.browser.close();
      } catch (error) {
        logger.warn('Failed to close browser gracefully', 'BrowserSession');
      }
      this.browser = null;
    }

    this.domService = null;
  }

  async navigate(url: string): Promise<void> {
    if (!this.page) {
      throw new Error('Browser session not started');
    }

    try {
      logger.info(`Navigating to: ${url}`, 'BrowserSession');
      await this.page.goto(url, { waitUntil: 'domcontentloaded', timeout: this.profile.timeout });
      await this.page.waitForTimeout(1000); // Wait for page to stabilize
      logger.success(`Successfully navigated to: ${url}`, 'BrowserSession');
    } catch (error) {
      logger.error(`Failed to navigate to: ${url}`, error as Error, 'BrowserSession');
      throw error;
    }
  }

  async getDOMState(): Promise<DOMState> {
    if (!this.page || !this.domService) {
      throw new Error('Browser session not started');
    }

    try {
      return await this.domService.getDOMState();
    } catch (error) {
      logger.error('Failed to get DOM state', error as Error, 'BrowserSession');
      throw error;
    }
  }

  async click(index: number, xpath?: string, cssSelector?: string, text?: string, attributes?: Record<string, string>): Promise<boolean> {
    if (!this.page) {
      throw new Error('Browser session not started');
    }

    try {
      // Use enhanced multi-strategy element location
      const locator = { index, xpath, cssSelector, text, attributes };
      const element = await this.domService.locateElement(locator);

      if (!element) {
        throw new Error(`Failed to locate element with any strategy: ${JSON.stringify(locator)}`);
      }

      // Ensure element is visible and scrolled into view
      await element.scrollIntoViewIfNeeded();
      await this.page.waitForTimeout(100); // Brief wait for scroll

      // Store current state to detect navigation
      const currentUrl = this.page.url();
      const currentDOMHash = await this.getDOMStructureHash();

      // Perform the click with retry mechanism
      await this.performClickWithRetry(element, index);

      logger.info(`Successfully clicked element at index: ${index}`, 'BrowserSession');

      // Wait for potential navigation and page load
      const navigationDetected = await this.waitForPotentialNavigation(currentUrl, currentDOMHash);

      if (navigationDetected) {
        logger.info('🔄 Navigation detected after click, page state may have changed', 'BrowserSession');
      }

      return navigationDetected;
    } catch (error) {
      logger.error(`Failed to click element at index ${index}`, error as Error, 'BrowserSession');

      // Try to relocate element if it was previously found
      try {
        const cachedElement = this.domService['elementCache'].get(`index_${index}`);
        if (cachedElement) {
          logger.info('Attempting to relocate element...', 'BrowserSession');
          const relocatedElement = await this.domService.relocateElement(cachedElement);
          await this.performClickWithRetry(relocatedElement, index);
          logger.info(`Successfully clicked relocated element at index: ${index}`, 'BrowserSession');
          return false; // No navigation detected in error case
        }
      } catch (relocateError) {
        logger.error('Element relocation failed', relocateError as Error, 'BrowserSession');
      }

      throw error;
    }
  }

  private async performClickWithRetry(element: any, index: number, maxRetries: number = 3): Promise<void> {
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        // Check if element is still attached and visible
        const isVisible = await element.isVisible();
        if (!isVisible) {
          throw new Error('Element is not visible');
        }

        // Try different click strategies
        if (attempt === 1) {
          // Standard click
          await element.click({ timeout: 5000 });
        } else if (attempt === 2) {
          // Force click (ignore covering elements)
          await element.click({ force: true, timeout: 5000 });
        } else {
          // JavaScript click as last resort
          await element.evaluate((el: HTMLElement) => el.click());
        }

        return; // Success
      } catch (error) {
        logger.warn(`Click attempt ${attempt}/${maxRetries} failed for element ${index}: ${error}`, 'BrowserSession');

        if (attempt === maxRetries) {
          throw error;
        }

        // Wait before retry
        await this.page.waitForTimeout(500 * attempt);
      }
    }
  }

  async type(index: number, text: string, xpath?: string, cssSelector?: string, attributes?: Record<string, string>): Promise<void> {
    if (!this.page) {
      throw new Error('Browser session not started');
    }

    try {
      // Use enhanced multi-strategy element location
      const locator = { index, xpath, cssSelector, text: undefined, attributes };
      const element = await this.domService.locateElement(locator);

      if (!element) {
        throw new Error(`Failed to locate input element with any strategy: ${JSON.stringify(locator)}`);
      }

      // Ensure element is visible and scrolled into view
      await element.scrollIntoViewIfNeeded();
      await this.page.waitForTimeout(100);

      // Store current state to detect navigation
      const currentUrl = this.page.url();
      const currentDOMHash = await this.getDOMStructureHash();

      // Perform typing with retry mechanism
      await this.performTypeWithRetry(element, text, index);

      logger.info(`Successfully typed text into element at index: ${index}`, 'BrowserSession');

      // Wait for potential navigation after typing (e.g., auto-submit forms)
      const navigationDetected = await this.waitForPotentialNavigation(currentUrl, currentDOMHash);

      if (navigationDetected) {
        logger.info('🔄 Navigation detected after typing, page state may have changed', 'BrowserSession');
      }
    } catch (error) {
      logger.error(`Failed to type into element at index ${index}`, error as Error, 'BrowserSession');

      // Try to relocate element if it was previously found
      try {
        const cachedElement = this.domService['elementCache'].get(`index_${index}`);
        if (cachedElement) {
          logger.info('Attempting to relocate input element...', 'BrowserSession');
          const currentUrl = this.page.url();
          const currentDOMHash = await this.getDOMStructureHash();
          const relocatedElement = await this.domService.relocateElement(cachedElement);
          await this.performTypeWithRetry(relocatedElement, text, index);
          logger.info(`Successfully typed into relocated element at index: ${index}`, 'BrowserSession');

          // Wait for potential navigation after typing (e.g., auto-submit forms)
          const navigationDetected = await this.waitForPotentialNavigation(currentUrl, currentDOMHash);

          if (navigationDetected) {
            logger.info('🔄 Navigation detected after typing (relocated), page state may have changed', 'BrowserSession');
          }
          return;
        }
      } catch (relocateError) {
        logger.error('Input element relocation failed', relocateError as Error, 'BrowserSession');
      }

      throw error;
    }
  }

  private async performTypeWithRetry(element: any, text: string, index: number, maxRetries: number = 3): Promise<void> {
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        // Check if element is still attached and visible
        const isVisible = await element.isVisible();
        if (!isVisible) {
          throw new Error('Input element is not visible');
        }

        // Check if element is editable
        const isEditable = await element.isEditable();
        if (!isEditable) {
          throw new Error('Element is not editable');
        }

        // Try different typing strategies
        if (attempt === 1) {
          // Standard fill
          await element.fill(text, { timeout: 5000 });
        } else if (attempt === 2) {
          // Clear and type
          await element.clear();
          await element.type(text, { delay: 50 });
        } else {
          // JavaScript value setting as last resort
          await element.evaluate((el: HTMLInputElement, value: string) => {
            el.value = value;
            el.dispatchEvent(new Event('input', { bubbles: true }));
            el.dispatchEvent(new Event('change', { bubbles: true }));
          }, text);
        }

        return; // Success
      } catch (error) {
        logger.warn(`Type attempt ${attempt}/${maxRetries} failed for element ${index}: ${error}`, 'BrowserSession');

        if (attempt === maxRetries) {
          throw error;
        }

        // Wait before retry
        await this.page.waitForTimeout(300 * attempt);
      }
    }
  }

  async scroll(direction: 'up' | 'down', amount?: number): Promise<void> {
    if (!this.page) {
      throw new Error('Browser session not started');
    }

    try {
      const scrollAmount = amount || 500;
      const scrollDirection = direction === 'down' ? scrollAmount : -scrollAmount;
      
      await this.page.evaluate((delta) => {
        window.scrollBy(0, delta);
      }, scrollDirection);

      logger.info(`Scrolled ${direction} by ${Math.abs(scrollDirection)}px`, 'BrowserSession');
      await this.page.waitForTimeout(500); // Wait for scroll to complete
    } catch (error) {
      logger.error(`Failed to scroll ${direction}`, error as Error, 'BrowserSession');
      throw error;
    }
  }

  async wait(seconds: number): Promise<void> {
    logger.info(`Waiting for ${seconds} seconds...`, 'BrowserSession');
    await new Promise(resolve => setTimeout(resolve, seconds * 1000));
  }

  async takeScreenshot(): Promise<string> {
    if (!this.page) {
      throw new Error('Browser session not started');
    }

    try {
      const screenshot = await this.page.screenshot({
        type: 'png',
        fullPage: false
      });
      return screenshot.toString('base64');
    } catch (error) {
      logger.error('Failed to take screenshot', error as Error, 'BrowserSession');
      throw error;
    }
  }

  async getCurrentState(): Promise<DOMState> {
    if (!this.page) {
      throw new Error('Browser session not started');
    }

    try {
      return await this.domService.getDOMState();
    } catch (error) {
      logger.error('Failed to get current state', error as Error, 'BrowserSession');
      throw error;
    }
  }

  getCurrentUrl(): string {
    if (!this.page) {
      throw new Error('Browser session not started');
    }
    return this.page.url();
  }

  async getCurrentTitle(): Promise<string> {
    if (!this.page) {
      throw new Error('Browser session not started');
    }
    return await this.page.title();
  }

  isStarted(): boolean {
    return this.browser !== null && this.context !== null && this.page !== null;
  }

  // Advanced browser features
  async hover(index: number, xpath?: string): Promise<void> {
    if (!this.page) {
      throw new Error('Browser session not started');
    }

    try {
      if (xpath) {
        const element = this.page.locator(`xpath=${xpath}`);
        await element.hover();
        logger.info(`Hovered over element with xpath: ${xpath}`, 'BrowserSession');
      } else {
        const elements = await this.page.locator('[data-browser-use-index]').all();
        if (index >= 0 && index < elements.length) {
          await elements[index].hover();
          logger.info(`Hovered over element at index: ${index}`, 'BrowserSession');
        } else {
          throw new Error(`Element index ${index} out of range`);
        }
      }
      await this.page.waitForTimeout(300);
    } catch (error) {
      logger.error(`Failed to hover over element`, error as Error, 'BrowserSession');
      throw error;
    }
  }

  async dragAndDrop(sourceIndex: number, targetIndex: number, sourceXpath?: string, targetXpath?: string): Promise<void> {
    if (!this.page) {
      throw new Error('Browser session not started');
    }

    try {
      let sourceElement, targetElement;

      if (sourceXpath && targetXpath) {
        sourceElement = this.page.locator(`xpath=${sourceXpath}`);
        targetElement = this.page.locator(`xpath=${targetXpath}`);
      } else {
        const elements = await this.page.locator('[data-browser-use-index]').all();
        if (sourceIndex >= elements.length || targetIndex >= elements.length) {
          throw new Error('Element index out of range');
        }
        sourceElement = elements[sourceIndex];
        targetElement = elements[targetIndex];
      }

      await sourceElement.dragTo(targetElement);
      logger.info(`Dragged element from ${sourceIndex} to ${targetIndex}`, 'BrowserSession');
      await this.page.waitForTimeout(500);
    } catch (error) {
      logger.error(`Failed to drag and drop`, error as Error, 'BrowserSession');
      throw error;
    }
  }

  async pressKey(key: string, modifiers?: string[]): Promise<boolean> {
    if (!this.page) {
      throw new Error('Browser session not started');
    }

    try {
      // Store current state to detect navigation
      const currentUrl = this.page.url();
      const currentDOMHash = await this.getDOMStructureHash();

      if (modifiers && modifiers.length > 0) {
        const modifierString = modifiers.join('+') + '+' + key;
        await this.page.keyboard.press(modifierString);
        logger.info(`Pressed key combination: ${modifierString}`, 'BrowserSession');
      } else {
        await this.page.keyboard.press(key);
        logger.info(`Pressed key: ${key}`, 'BrowserSession');
      }

      // Wait for potential navigation after key press (e.g., Enter key)
      const navigationDetected = await this.waitForPotentialNavigation(currentUrl, currentDOMHash);

      if (navigationDetected) {
        logger.info('🔄 Navigation detected after key press, page state may have changed', 'BrowserSession');
      }

      return navigationDetected;
    } catch (error) {
      logger.error(`Failed to press key`, error as Error, 'BrowserSession');
      throw error;
    }
  }

  async selectOption(index: number, value: string | string[], xpath?: string): Promise<void> {
    if (!this.page) {
      throw new Error('Browser session not started');
    }

    try {
      if (xpath) {
        const element = this.page.locator(`xpath=${xpath}`);
        await element.selectOption(value);
        logger.info(`Selected option in element with xpath: ${xpath}`, 'BrowserSession');
      } else {
        const elements = await this.page.locator('[data-browser-use-index]').all();
        if (index >= 0 && index < elements.length) {
          await elements[index].selectOption(value);
          logger.info(`Selected option in element at index: ${index}`, 'BrowserSession');
        } else {
          throw new Error(`Element index ${index} out of range`);
        }
      }
      await this.page.waitForTimeout(500);
    } catch (error) {
      logger.error(`Failed to select option`, error as Error, 'BrowserSession');
      throw error;
    }
  }

  async uploadFile(index: number, filePath: string, xpath?: string): Promise<void> {
    if (!this.page) {
      throw new Error('Browser session not started');
    }

    try {
      if (xpath) {
        const element = this.page.locator(`xpath=${xpath}`);
        await element.setInputFiles(filePath);
        logger.info(`Uploaded file to element with xpath: ${xpath}`, 'BrowserSession');
      } else {
        const elements = await this.page.locator('[data-browser-use-index]').all();
        if (index >= 0 && index < elements.length) {
          await elements[index].setInputFiles(filePath);
          logger.info(`Uploaded file to element at index: ${index}`, 'BrowserSession');
        } else {
          throw new Error(`Element index ${index} out of range`);
        }
      }
      await this.page.waitForTimeout(1000);
    } catch (error) {
      logger.error(`Failed to upload file`, error as Error, 'BrowserSession');
      throw error;
    }
  }

  async executeScript(script: string, args?: any[]): Promise<any> {
    if (!this.page) {
      throw new Error('Browser session not started');
    }

    try {
      const result = await this.page.evaluate(script, args);
      logger.info(`Executed script successfully`, 'BrowserSession');
      return result;
    } catch (error) {
      logger.error(`Failed to execute script`, error as Error, 'BrowserSession');
      throw error;
    }
  }

  async newTab(url?: string): Promise<number> {
    if (!this.context) {
      throw new Error('Browser session not started');
    }

    try {
      const newPage = await this.context.newPage();
      this.tabs.push(newPage);
      const tabIndex = this.tabs.length - 1;

      if (url) {
        await newPage.goto(url, { waitUntil: 'domcontentloaded' });
      }

      logger.info(`Created new tab (index: ${tabIndex})${url ? ` and navigated to ${url}` : ''}`, 'BrowserSession');
      return tabIndex;
    } catch (error) {
      logger.error(`Failed to create new tab`, error as Error, 'BrowserSession');
      throw error;
    }
  }

  async switchTab(tabIndex: number): Promise<void> {
    if (tabIndex < 0 || tabIndex >= this.tabs.length) {
      throw new Error(`Tab index ${tabIndex} out of range`);
    }

    try {
      this.currentTabIndex = tabIndex;
      this.page = this.tabs[tabIndex];

      if (this.domService) {
        this.domService = new DOMService(this.page);
      }

      await this.page.bringToFront();
      logger.info(`Switched to tab ${tabIndex}`, 'BrowserSession');
    } catch (error) {
      logger.error(`Failed to switch tab`, error as Error, 'BrowserSession');
      throw error;
    }
  }

  async closeTab(tabIndex?: number): Promise<void> {
    const indexToClose = tabIndex ?? this.currentTabIndex;

    if (indexToClose < 0 || indexToClose >= this.tabs.length) {
      throw new Error(`Tab index ${indexToClose} out of range`);
    }

    if (this.tabs.length === 1) {
      throw new Error('Cannot close the last tab');
    }

    try {
      await this.tabs[indexToClose].close();
      this.tabs.splice(indexToClose, 1);

      // Adjust current tab index if necessary
      if (this.currentTabIndex >= indexToClose && this.currentTabIndex > 0) {
        this.currentTabIndex--;
      }

      // Switch to the current tab
      this.page = this.tabs[this.currentTabIndex];
      if (this.domService) {
        this.domService = new DOMService(this.page);
      }

      logger.info(`Closed tab ${indexToClose}`, 'BrowserSession');
    } catch (error) {
      logger.error(`Failed to close tab`, error as Error, 'BrowserSession');
      throw error;
    }
  }

  async goBack(): Promise<void> {
    if (!this.page) {
      throw new Error('Browser session not started');
    }

    try {
      await this.page.goBack({ waitUntil: 'domcontentloaded' });
      logger.info('Navigated back', 'BrowserSession');
      await this.page.waitForTimeout(1000);
    } catch (error) {
      logger.error('Failed to go back', error as Error, 'BrowserSession');
      throw error;
    }
  }

  async goForward(): Promise<void> {
    if (!this.page) {
      throw new Error('Browser session not started');
    }

    try {
      await this.page.goForward({ waitUntil: 'domcontentloaded' });
      logger.info('Navigated forward', 'BrowserSession');
      await this.page.waitForTimeout(1000);
    } catch (error) {
      logger.error('Failed to go forward', error as Error, 'BrowserSession');
      throw error;
    }
  }

  async refresh(): Promise<void> {
    if (!this.page) {
      throw new Error('Browser session not started');
    }

    try {
      await this.page.reload({ waitUntil: 'domcontentloaded' });
      logger.info('Page refreshed', 'BrowserSession');
      await this.page.waitForTimeout(1000);
    } catch (error) {
      logger.error('Failed to refresh page', error as Error, 'BrowserSession');
      throw error;
    }
  }

  async setCookie(name: string, value: string, options?: {
    domain?: string;
    path?: string;
    expires?: number;
    httpOnly?: boolean;
    secure?: boolean;
    sameSite?: 'Strict' | 'Lax' | 'None';
  }): Promise<void> {
    if (!this.context) {
      throw new Error('Browser session not started');
    }

    try {
      const cookieData: any = {
        name,
        value,
        domain: options?.domain || new URL(this.getCurrentUrl()).hostname,
        path: options?.path || '/',
      };

      if (options?.expires !== undefined) cookieData.expires = options.expires;
      if (options?.httpOnly !== undefined) cookieData.httpOnly = options.httpOnly;
      if (options?.secure !== undefined) cookieData.secure = options.secure;
      if (options?.sameSite) cookieData.sameSite = options.sameSite;

      await this.context.addCookies([cookieData]);
      logger.info(`Set cookie: ${name}`, 'BrowserSession');
    } catch (error) {
      logger.error('Failed to set cookie', error as Error, 'BrowserSession');
      throw error;
    }
  }

  async getCookies(): Promise<any[]> {
    if (!this.context) {
      throw new Error('Browser session not started');
    }

    try {
      return await this.context.cookies();
    } catch (error) {
      logger.error('Failed to get cookies', error as Error, 'BrowserSession');
      throw error;
    }
  }

  async waitForElement(selector: string, timeout: number = 30000, state: 'visible' | 'hidden' | 'attached' | 'detached' = 'visible'): Promise<void> {
    if (!this.page) {
      throw new Error('Browser session not started');
    }

    try {
      await this.page.waitForSelector(selector, { timeout, state });
      logger.info(`Element found: ${selector}`, 'BrowserSession');
    } catch (error) {
      logger.error(`Failed to wait for element: ${selector}`, error as Error, 'BrowserSession');
      throw error;
    }
  }

  async waitForNavigation(timeout: number = 30000, waitUntil: 'load' | 'domcontentloaded' | 'networkidle' = 'domcontentloaded'): Promise<void> {
    if (!this.page) {
      throw new Error('Browser session not started');
    }

    try {
      await this.page.waitForLoadState(waitUntil, { timeout });
      logger.info('Navigation completed', 'BrowserSession');
    } catch (error) {
      logger.error('Failed to wait for navigation', error as Error, 'BrowserSession');
      throw error;
    }
  }

  async extractData(selector?: string, xpath?: string, attribute?: string, multiple: boolean = false): Promise<string | string[]> {
    if (!this.page) {
      throw new Error('Browser session not started');
    }

    try {
      let locator: any;
      if (xpath) {
        locator = this.page.locator(`xpath=${xpath}`);
      } else if (selector) {
        locator = this.page.locator(selector);
      } else {
        throw new Error('Either selector or xpath must be provided');
      }

      if (multiple) {
        const elements = await locator.all();
        const results: string[] = [];

        for (const element of elements) {
          if (attribute) {
            const value = await element.getAttribute(attribute);
            results.push(value || '');
          } else {
            const text = await element.textContent();
            results.push(text || '');
          }
        }

        logger.info(`Extracted ${results.length} values`, 'BrowserSession');
        return results;
      } else {
        let result: string;
        if (attribute) {
          result = await locator.getAttribute(attribute) || '';
        } else {
          result = await locator.textContent() || '';
        }

        logger.info(`Extracted data: ${result.substring(0, 50)}...`, 'BrowserSession');
        return result;
      }
    } catch (error) {
      logger.error('Failed to extract data', error as Error, 'BrowserSession');
      throw error;
    }
  }

  getTabCount(): number {
    return this.tabs.length;
  }

  getCurrentTabIndex(): number {
    return this.currentTabIndex;
  }

  private setupEventListeners(): void {
    if (!this.page) return;

    this.page.on('console', msg => {
      if (msg.type() === 'error') {
        logger.debug(`Browser console error: ${msg.text()}`, 'BrowserSession');
      }
    });

    this.page.on('pageerror', error => {
      logger.debug(`Page error: ${error.message}`, 'BrowserSession');
    });

    this.page.on('requestfailed', request => {
      logger.debug(`Request failed: ${request.url()}`, 'BrowserSession');
    });
  }

  /**
   * Wait for potential navigation after an action (click, key press, etc.)
   * This method detects URL changes, DOM changes, and waits for page load completion
   * Similar to Python version's navigation detection logic
   */
  private async waitForPotentialNavigation(previousUrl: string, previousDOMHash?: string, timeout: number = 10000): Promise<boolean> {
    if (!this.page) return false;

    try {
      logger.info(`Checking for navigation from: ${previousUrl}`, 'BrowserSession');

      // Wait a short time to see if navigation starts
      await this.page.waitForTimeout(100);

      const currentUrl = this.page.url();
      logger.info(`Current URL after action: ${currentUrl}`, 'BrowserSession');

      // Check for URL change (traditional navigation)
      if (currentUrl !== previousUrl) {
        logger.info(`🔄 URL Navigation detected: ${previousUrl} → ${currentUrl}`, 'BrowserSession');

        try {
          // Wait for page to load completely
          await this.page.waitForLoadState('domcontentloaded', { timeout });
          logger.info('✅ URL Navigation completed successfully', 'BrowserSession');
          return true;
        } catch (error) {
          logger.warn(`⚠️ Navigation timeout or failed, continuing anyway: ${error}`, 'BrowserSession');
          return true; // Still consider it navigation even if timeout
        }
      }

      // Check for DOM structure change (SPA navigation)
      if (previousDOMHash) {
        try {
          // Wait a bit more for SPA content to load
          await this.page.waitForTimeout(1000);

          // Get current DOM hash to compare
          const currentDOMHash = await this.getDOMStructureHash();

          if (currentDOMHash !== previousDOMHash) {
            logger.info(`🔄 SPA Navigation detected: DOM structure changed`, 'BrowserSession');
            logger.info(`Previous DOM hash: ${previousDOMHash.substring(0, 10)}...`, 'BrowserSession');
            logger.info(`Current DOM hash: ${currentDOMHash.substring(0, 10)}...`, 'BrowserSession');

            // CRITICAL: Wait longer for SPA content to fully load
            logger.info('⏳ Waiting for SPA content to fully load...', 'BrowserSession');
            await this.page.waitForTimeout(2000);

            // Also wait for any network requests to complete
            try {
              await this.page.waitForLoadState('networkidle', { timeout: 5000 });
              logger.info('✅ Network idle achieved after SPA navigation', 'BrowserSession');
            } catch (networkError) {
              logger.warn(`⚠️ Network idle timeout, continuing anyway: ${networkError}`, 'BrowserSession');
            }

            return true;
          }
        } catch (error) {
          logger.warn(`Error checking DOM structure change: ${error}`, 'BrowserSession');
        }
      }

      logger.info('ℹ️ No navigation detected, waiting for dynamic content...', 'BrowserSession');
      // No navigation detected, just wait a bit for any dynamic content
      await this.page.waitForTimeout(500);
      return false;

    } catch (error) {
      logger.warn(`Error during navigation wait: ${error}`, 'BrowserSession');
      // Continue anyway, don't fail the entire action
      return false;
    }
  }

  /**
   * Get a hash of the current DOM structure to detect SPA navigation
   */
  private async getDOMStructureHash(): Promise<string> {
    try {
      const domStructure = await this.page.evaluate(() => {
        // Get a simplified representation of the DOM structure
        const getElementSignature = (element: Element): string => {
          const tag = element.tagName.toLowerCase();
          const id = element.id ? `#${element.id}` : '';
          const classes = element.className ? `.${element.className.split(' ').join('.')}` : '';
          const text = element.textContent?.trim().substring(0, 50) || '';
          return `${tag}${id}${classes}:${text}`;
        };

        // Get signatures of key elements that change during navigation
        const keyElements = document.querySelectorAll('main, [role="main"], .content, #content, .page, .container');
        const signatures = Array.from(keyElements).map(getElementSignature);

        // Also include page title and some meta info
        const title = document.title;
        const metaDescription = document.querySelector('meta[name="description"]')?.getAttribute('content') || '';

        return {
          title,
          metaDescription,
          keyElementSignatures: signatures,
          elementCount: document.querySelectorAll('*').length
        };
      });

      // Create a hash from the DOM structure
      const structureString = JSON.stringify(domStructure);
      return this.simpleHash(structureString);
    } catch (error) {
      logger.warn(`Error getting DOM structure hash: ${error}`, 'BrowserSession');
      return '';
    }
  }

  /**
   * Simple hash function for strings
   */
  private simpleHash(str: string): string {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32bit integer
    }
    return hash.toString();
  }
}
