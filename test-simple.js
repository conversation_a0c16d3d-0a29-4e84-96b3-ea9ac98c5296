const { Agent, BrowserSession, LLMFactory, Config } = require('./dist');

async function testSimple() {
  try {
    console.log('🔍 Simple Enhanced DOM Test\n');

    // Get configuration from environment
    const llmConfig = Config.getLLMConfig();
    const browserProfile = Config.getBrowserProfile();
    const agentSettings = {
      ...Config.getAgentSettings(),
      maxSteps: 5,
      enableLoopDetection: true,
      maxConsecutiveFailures: 3,
      maxSimilarActions: 2,
    };

    console.log(`Using LLM: ${llmConfig.provider} - ${llmConfig.model}`);
    console.log(`Browser mode: ${browserProfile.headless ? 'headless' : 'visible'}`);

    // Create LLM instance
    const llm = LLMFactory.createLLM(llmConfig);

    // Create browser session
    const browserProfile2 = { ...browserProfile, headless: false };
    const browserSession = new BrowserSession(browserProfile2);
    await browserSession.start();

    // Simple test: just navigate to a page
    console.log('📋 Test: Simple Navigation');
    const task = "访问百度首页";
    
    const agent = new Agent(task, llm, browserSession, agentSettings);
    const history = await agent.run();
    
    console.log('\n📊 Test Results:');
    console.log(`- Total steps: ${history.steps.length}`);
    console.log(`- Success: ${history.success}`);
    console.log(`- Duration: ${history.totalDuration.toFixed(2)}s`);
    
    // Test DOM state retrieval
    console.log('\n🔍 Testing DOM State Retrieval:');
    try {
      const domState = await browserSession.getCurrentState();
      console.log(`✅ Successfully retrieved DOM state`);
      console.log(`✅ Found ${domState.elements.length} interactive elements`);
      
      if (domState.elements.length > 0) {
        const firstElement = domState.elements[0];
        console.log(`✅ First element: ${firstElement.tag} - "${firstElement.text}"`);
        console.log(`✅ Has CSS selector: ${firstElement.cssSelector ? 'Yes' : 'No'}`);
        console.log(`✅ Has XPath: ${firstElement.xpath ? 'Yes' : 'No'}`);
      }
    } catch (error) {
      console.log(`❌ DOM state retrieval failed: ${error.message}`);
    }

    await browserSession.close();
    
    console.log('\n🎉 Simple test completed!');

  } catch (error) {
    console.error('❌ Test failed:', error);
    process.exit(1);
  }
}

testSimple().catch(console.error);
