const { BrowserSession, Config } = require('./dist');
const { DOMService } = require('./dist/dom/service');

async function testDOMService() {
  try {
    console.log('🔍 Testing DOM Service on Bilibili\n');

    // Create browser session
    const browserProfile = { ...Config.getBrowserProfile(), headless: false };
    const browserSession = new BrowserSession(browserProfile);
    await browserSession.start();

    // Navigate to Bilibili
    console.log('📋 Navigating to Bilibili...');
    await browserSession.navigate('https://www.bilibili.com');
    
    // Wait for page to load
    await new Promise(resolve => setTimeout(resolve, 5000));

    console.log('📋 Testing DOM Service directly...');

    // Create DOM service instance
    const domService = new DOMService(browserSession.page);

    try {
      // Test buildCompleteDOM method directly
      console.log('Testing buildCompleteDOM...');
      const elements = await domService.buildCompleteDOM(-1, 0);
      console.log(`✅ buildCompleteDOM succeeded: ${elements.length} elements`);
      
      if (elements.length > 0) {
        console.log(`✅ First element: ${elements[0].tag} - "${elements[0].text}"`);
      }
    } catch (error) {
      console.log(`❌ buildCompleteDOM failed: ${error.message}`);
      console.log(`❌ Stack: ${error.stack?.substring(0, 300)}...`);
    }

    try {
      // Test getDOMState method
      console.log('\nTesting getDOMState...');
      const domState = await domService.getDOMState(-1, 0);
      console.log(`✅ getDOMState succeeded: ${domState.elements.length} elements`);
      
      if (domState.elements.length > 0) {
        console.log(`✅ First element: ${domState.elements[0].tag} - "${domState.elements[0].text}"`);
      }
    } catch (error) {
      console.log(`❌ getDOMState failed: ${error.message}`);
      console.log(`❌ Stack: ${error.stack?.substring(0, 300)}...`);
    }

    try {
      // Test browser session getCurrentState
      console.log('\nTesting BrowserSession.getCurrentState...');
      const currentState = await browserSession.getCurrentState();
      console.log(`✅ getCurrentState succeeded: ${currentState.elements.length} elements`);
      
      if (currentState.elements.length > 0) {
        console.log(`✅ First element: ${currentState.elements[0].tag} - "${currentState.elements[0].text}"`);
      }
    } catch (error) {
      console.log(`❌ getCurrentState failed: ${error.message}`);
      console.log(`❌ Stack: ${error.stack?.substring(0, 300)}...`);
    }

    await browserSession.close();
    
    console.log('\n🎉 DOM Service test completed!');

  } catch (error) {
    console.error('❌ Test failed:', error);
    console.error('❌ Stack:', error.stack);
    process.exit(1);
  }
}

testDOMService().catch(console.error);
