import { logger } from '../utils/logger';

/**
 * 🏷️ 智能标签页管理器 - 基于参考文件重构
 */
export interface TabInfo {
  id: string;
  url: string;
  title: string;
  isActive: boolean;
  lastAccessed: number;
  score: number;
  metadata?: {
    hasSearchResults?: boolean;
    contentType?: string;
    loadTime?: number;
    errorCount?: number;
  };
}

export interface TabSwitchResult {
  success: boolean;
  previousTab?: TabInfo;
  currentTab?: TabInfo;
  message?: string;
}

export class SmartTabManager {
  private browser: any;
  private context: any;
  private tabs = new Map<any, TabInfo>();
  private activeTab: any = null;
  private tabSwitchListeners: Array<(result: TabSwitchResult) => Promise<void>> = [];
  private monitoringActive = false;
  private tabCheckInterval: any = null;

  constructor(browser: any, context: any) {
    this.browser = browser;
    this.context = context;
  }

  /**
   * 启动标签页监控
   */
  async startMonitoring(): Promise<void> {
    if (this.monitoringActive) return;
    
    logger.info('🔄 启动智能标签页管理器...', 'SmartTabManager');
    
    try {
      // 监听新标签页创建
      this.context.on('page', (page: any) => {
        this.handleNewTab(page).catch(error => {
          logger.warn(`处理新标签页失败: ${error.message}`, 'SmartTabManager');
        });
      });
      
      // 定期检查标签页状态
      this.tabCheckInterval = setInterval(() => {
        this.updateTabScores().catch(error => {
          logger.debug(`更新标签页评分失败: ${error}`, 'SmartTabManager');
        });
      }, 5000);
      
      this.monitoringActive = true;
      
      // 初始化现有标签页
      await this.initializeExistingTabs();
      
      logger.info('✅ 智能标签页管理器已启动', 'SmartTabManager');
    } catch (error) {
      logger.error('❌ 智能标签页管理器启动失败', error as Error, 'SmartTabManager');
    }
  }

  /**
   * 处理新标签页
   */
  private async handleNewTab(page: any): Promise<void> {
    try {
      logger.info('🆕 检测到新标签页', 'SmartTabManager');
      
      // 等待页面加载
      await page.waitForLoadState('domcontentloaded', { timeout: 10000 });
      
      const tabInfo: TabInfo = {
        id: this.generateTabId(page),
        url: page.url(),
        title: await page.title(),
        isActive: false,
        lastAccessed: Date.now(),
        score: 0,
        metadata: {
          hasSearchResults: false,
          contentType: 'unknown',
          loadTime: Date.now(),
          errorCount: 0
        }
      };
      
      this.tabs.set(page, tabInfo);
      
      // 分析标签页内容
      await this.analyzeTabContent(page, tabInfo);
      
      logger.info(`✅ 新标签页已注册: ${tabInfo.title} (${tabInfo.url})`, 'SmartTabManager');
    } catch (error) {
      logger.warn(`处理新标签页失败: ${error}`, 'SmartTabManager');
    }
  }

  /**
   * 初始化现有标签页
   */
  private async initializeExistingTabs(): Promise<void> {
    try {
      const pages = this.context.pages();
      
      for (const page of pages) {
        await this.handleNewTab(page);
      }
      
      // 设置当前活动标签页
      if (pages.length > 0) {
        this.activeTab = pages[0];
        const tabInfo = this.tabs.get(this.activeTab);
        if (tabInfo) {
          tabInfo.isActive = true;
        }
      }
    } catch (error) {
      logger.warn(`初始化现有标签页失败: ${error}`, 'SmartTabManager');
    }
  }

  /**
   * 分析标签页内容
   */
  private async analyzeTabContent(page: any, tabInfo: TabInfo): Promise<void> {
    try {
      const analysis = await page.evaluate(() => {
        // 检查是否有搜索结果
        const searchResultSelectors = [
          '.search-result', '.video-item', '.bili-video-card',
          '.search-page', '[class*="search"]', '[class*="video"]',
          '.video-list', '.result-item', '.card-box'
        ];
        
        let hasSearchResults = false;
        for (const selector of searchResultSelectors) {
          if (document.querySelector(selector)) {
            hasSearchResults = true;
            break;
          }
        }
        
        // 检查内容类型
        const bodyText = document.body?.innerText || '';
        let contentType = 'unknown';
        
        if (bodyText.includes('视频') || bodyText.includes('播放')) {
          contentType = 'video';
        } else if (bodyText.includes('搜索') || bodyText.includes('结果')) {
          contentType = 'search';
        } else if (bodyText.includes('首页') || bodyText.includes('主页')) {
          contentType = 'homepage';
        }
        
        return {
          hasSearchResults,
          contentType,
          elementCount: document.querySelectorAll('*').length,
          interactiveElementCount: document.querySelectorAll('button, a, input, select, textarea').length
        };
      });
      
      if (tabInfo.metadata) {
        tabInfo.metadata.hasSearchResults = analysis.hasSearchResults;
        tabInfo.metadata.contentType = analysis.contentType;
      }
      
      // 计算初始评分
      tabInfo.score = this.calculateTabScore(tabInfo, analysis);
      
      logger.debug(`📊 标签页内容分析完成: ${tabInfo.title} (评分: ${tabInfo.score})`, 'SmartTabManager');
    } catch (error) {
      logger.debug(`分析标签页内容失败: ${error}`, 'SmartTabManager');
    }
  }

  /**
   * 计算标签页评分
   */
  private calculateTabScore(tabInfo: TabInfo, analysis?: any): number {
    let score = 0;
    
    // 基础分数
    score += 10;
    
    // 内容类型加分
    if (tabInfo.metadata?.contentType === 'video') {
      score += 30;
    } else if (tabInfo.metadata?.contentType === 'search') {
      score += 20;
    } else if (tabInfo.metadata?.contentType === 'homepage') {
      score += 5;
    }
    
    // 搜索结果加分
    if (tabInfo.metadata?.hasSearchResults) {
      score += 25;
    }
    
    // 最近访问加分
    const timeSinceAccess = Date.now() - tabInfo.lastAccessed;
    if (timeSinceAccess < 30000) { // 30秒内
      score += 15;
    } else if (timeSinceAccess < 120000) { // 2分钟内
      score += 10;
    }
    
    // 错误次数扣分
    if (tabInfo.metadata?.errorCount) {
      score -= tabInfo.metadata.errorCount * 5;
    }
    
    // 活动状态加分
    if (tabInfo.isActive) {
      score += 20;
    }
    
    return Math.max(0, score);
  }

  /**
   * 更新所有标签页评分
   */
  private async updateTabScores(): Promise<void> {
    if (!this.monitoringActive) return;
    
    for (const [page, tabInfo] of this.tabs.entries()) {
      try {
        // 更新基本信息
        tabInfo.url = page.url();
        tabInfo.title = await page.title();
        
        // 重新计算评分
        tabInfo.score = this.calculateTabScore(tabInfo);
      } catch (error) {
        // 标签页可能已关闭
        this.tabs.delete(page);
      }
    }
  }

  /**
   * 智能切换到最佳标签页
   */
  async switchToOptimalTab(criteria?: {
    preferSearchResults?: boolean;
    preferVideoContent?: boolean;
    excludeCurrentTab?: boolean;
  }): Promise<TabSwitchResult> {
    try {
      logger.info('🔄 开始智能标签页切换...', 'SmartTabManager');
      
      const previousTab = this.activeTab ? this.tabs.get(this.activeTab) : undefined;
      
      // 获取所有候选标签页
      let candidates = Array.from(this.tabs.entries());
      
      // 应用筛选条件
      if (criteria?.excludeCurrentTab && this.activeTab) {
        candidates = candidates.filter(([page]) => page !== this.activeTab);
      }
      
      if (criteria?.preferSearchResults) {
        candidates = candidates.filter(([, tabInfo]) => tabInfo.metadata?.hasSearchResults);
      }
      
      if (criteria?.preferVideoContent) {
        candidates = candidates.filter(([, tabInfo]) => tabInfo.metadata?.contentType === 'video');
      }
      
      if (candidates.length === 0) {
        return {
          success: false,
          message: '没有找到符合条件的标签页'
        };
      }
      
      // 按评分排序
      candidates.sort(([, a], [, b]) => b.score - a.score);
      
      const [bestPage, bestTabInfo] = candidates[0];
      
      // 执行切换
      await bestPage.bringToFront();
      
      // 更新状态
      if (this.activeTab && this.tabs.has(this.activeTab)) {
        this.tabs.get(this.activeTab)!.isActive = false;
      }
      
      this.activeTab = bestPage;
      bestTabInfo.isActive = true;
      bestTabInfo.lastAccessed = Date.now();
      
      const result: TabSwitchResult = {
        success: true,
        previousTab,
        currentTab: bestTabInfo,
        message: `已切换到: ${bestTabInfo.title}`
      };
      
      // 通知监听器
      await this.notifyTabSwitch(result);
      
      logger.info(`✅ 智能标签页切换完成: ${bestTabInfo.title} (评分: ${bestTabInfo.score})`, 'SmartTabManager');
      
      return result;
    } catch (error) {
      logger.error('❌ 智能标签页切换失败', error as Error, 'SmartTabManager');
      return {
        success: false,
        message: `切换失败: ${error}`
      };
    }
  }

  /**
   * 生成标签页ID
   */
  private generateTabId(page: any): string {
    return `tab_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 通知标签页切换监听器
   */
  private async notifyTabSwitch(result: TabSwitchResult): Promise<void> {
    for (const listener of this.tabSwitchListeners) {
      try {
        await listener(result);
      } catch (error) {
        logger.warn(`标签页切换监听器执行失败: ${error}`, 'SmartTabManager');
      }
    }
  }

  /**
   * 添加标签页切换监听器
   */
  addTabSwitchListener(listener: (result: TabSwitchResult) => Promise<void>): void {
    this.tabSwitchListeners.push(listener);
    logger.debug('✅ 已添加标签页切换监听器', 'SmartTabManager');
  }

  /**
   * 获取当前活动标签页
   */
  getCurrentTab(): TabInfo | undefined {
    return this.activeTab ? this.tabs.get(this.activeTab) : undefined;
  }

  /**
   * 获取所有标签页信息
   */
  getAllTabs(): TabInfo[] {
    return Array.from(this.tabs.values());
  }

  /**
   * 获取最佳标签页（按评分）
   */
  getBestTab(): TabInfo | undefined {
    const tabs = this.getAllTabs();
    if (tabs.length === 0) return undefined;
    
    return tabs.reduce((best, current) => 
      current.score > best.score ? current : best
    );
  }

  /**
   * 停止监控
   */
  stopMonitoring(): void {
    if (!this.monitoringActive) return;
    
    logger.info('🛑 停止智能标签页管理器...', 'SmartTabManager');
    
    this.monitoringActive = false;
    
    if (this.tabCheckInterval) {
      clearInterval(this.tabCheckInterval);
      this.tabCheckInterval = null;
    }
    
    logger.info('✅ 智能标签页管理器已停止', 'SmartTabManager');
  }
}
