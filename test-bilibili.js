const { BrowserSession, Config } = require('./dist');

async function testBilibili() {
  try {
    console.log('🔍 Testing Bilibili DOM Analysis\n');

    // Create browser session
    const browserProfile = { ...Config.getBrowserProfile(), headless: false };
    const browserSession = new BrowserSession(browserProfile);
    await browserSession.start();

    // Navigate to Bilibili
    console.log('📋 Navigating to Bilibili...');
    await browserSession.navigate('https://www.bilibili.com');
    
    // Wait for page to load
    await new Promise(resolve => setTimeout(resolve, 5000));

    console.log('📋 Testing DOM Script on Bilibili...');

    // Read the script
    const fs = require('fs');
    const path = require('path');
    const scriptPath = path.join(__dirname, 'src', 'dom', 'buildDomTree.js');
    const domScript = fs.readFileSync(scriptPath, 'utf8');

    // Test the script directly with error handling
    const result = await browserSession.page.evaluate(({ script, args }) => {
      try {
        const fn = eval(script);
        return fn(args);
      } catch (error) {
        return {
          error: true,
          message: error.message,
          stack: error.stack,
          name: error.name
        };
      }
    }, {
      script: domScript,
      args: {
        doHighlightElements: true,
        focusHighlightIndex: -1,
        viewportExpansion: 0,
        debugMode: true,
      }
    });

    console.log('📊 DOM Analysis Results:');

    if (result && result.error) {
      console.log(`❌ Script execution failed:`);
      console.log(`- Error: ${result.message}`);
      console.log(`- Name: ${result.name}`);
      console.log(`- Stack: ${result.stack?.substring(0, 300)}...`);
      return;
    }

    console.log(`- Success: ${!!result}`);
    console.log(`- Has rootId: ${!!result?.rootId}`);
    console.log(`- Has map: ${!!result?.map}`);
    console.log(`- Map size: ${result?.map ? Object.keys(result.map).length : 0}`);
    console.log(`- Has performance metrics: ${!!result?.perfMetrics}`);

    if (result?.perfMetrics) {
      console.log(`- Total nodes: ${result.perfMetrics.nodeMetrics?.totalNodes || 0}`);
      console.log(`- Processed nodes: ${result.perfMetrics.nodeMetrics?.processedNodes || 0}`);
      console.log(`- Skipped nodes: ${result.perfMetrics.nodeMetrics?.skippedNodes || 0}`);
    }

    // Count interactive elements
    let interactiveCount = 0;
    if (result?.map) {
      Object.values(result.map).forEach(node => {
        if (node.highlightIndex !== undefined) {
          interactiveCount++;
        }
      });
    }
    console.log(`- Interactive elements: ${interactiveCount}`);

    // Test DOM service
    console.log('\n📋 Testing DOM Service...');
    try {
      const domState = await browserSession.getCurrentState();
      console.log(`✅ DOM Service succeeded`);
      console.log(`✅ Found ${domState.elements.length} elements`);
      
      if (domState.elements.length > 0) {
        console.log(`✅ First element: ${domState.elements[0].tag} - "${domState.elements[0].text}"`);
      }
    } catch (error) {
      console.log(`❌ DOM Service failed: ${error.message}`);
      console.log(`❌ Error stack: ${error.stack?.substring(0, 200)}...`);
    }

    await browserSession.close();
    
    console.log('\n🎉 Bilibili test completed!');

  } catch (error) {
    console.error('❌ Test failed:', error);
    console.error('❌ Stack:', error.stack);
    process.exit(1);
  }
}

testBilibili().catch(console.error);
