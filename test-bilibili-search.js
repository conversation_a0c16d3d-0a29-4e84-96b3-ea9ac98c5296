const { chromium } = require('playwright');

async function testBilibiliSearch() {
  console.log('🚀 Starting Bilibili search test...');
  
  const browser = await chromium.launch({ 
    headless: false,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  const context = await browser.newContext({
    viewport: { width: 1280, height: 720 },
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
  });
  
  const page = await context.newPage();
  
  try {
    // 1. Navigate to Bilibili
    console.log('📍 Step 1: Navigating to Bilibili...');
    await page.goto('https://www.bilibili.com/', { waitUntil: 'networkidle' });
    await page.waitForTimeout(2000);
    
    console.log(`Current URL: ${page.url()}`);
    console.log(`Page title: ${await page.title()}`);
    
    // 2. Find and click search input
    console.log('📍 Step 2: Finding search input...');
    const searchInput = await page.locator('input[placeholder*="搜索"], input[class*="search"], .search-input input').first();
    
    if (await searchInput.count() === 0) {
      console.log('❌ Search input not found, trying alternative selectors...');
      const allInputs = await page.locator('input').all();
      console.log(`Found ${allInputs.length} input elements`);
      
      for (let i = 0; i < allInputs.length; i++) {
        const input = allInputs[i];
        const placeholder = await input.getAttribute('placeholder');
        const className = await input.getAttribute('class');
        console.log(`Input ${i}: placeholder="${placeholder}", class="${className}"`);
      }
      return;
    }
    
    await searchInput.click();
    await page.waitForTimeout(500);
    
    // 3. Type search term
    console.log('📍 Step 3: Typing search term...');
    await searchInput.fill('搞笑视频');
    await page.waitForTimeout(1000);
    
    console.log('Current URL after typing:', page.url());
    
    // 4. Press Enter to search
    console.log('📍 Step 4: Pressing Enter to search...');
    await searchInput.press('Enter');
    
    // 5. Wait for navigation
    console.log('📍 Step 5: Waiting for search results...');
    await page.waitForTimeout(3000);
    
    // Check if URL changed
    const finalUrl = page.url();
    const finalTitle = await page.title();
    
    console.log(`Final URL: ${finalUrl}`);
    console.log(`Final title: ${finalTitle}`);
    
    // Check for search results
    const hasSearchResults = await page.evaluate(() => {
      const searchResultSelectors = [
        '.search-result',
        '.video-item',
        '.bili-video-card',
        '.search-page',
        '[class*="search"]',
        '[class*="video"]'
      ];
      
      for (const selector of searchResultSelectors) {
        if (document.querySelector(selector)) {
          return { found: true, selector };
        }
      }
      
      // Check if body text contains search-related content
      const bodyText = document.body.innerText;
      const hasSearchContent = bodyText.includes('搞笑视频') || 
                              bodyText.includes('搜索结果') ||
                              bodyText.includes('视频') ||
                              !bodyText.includes('首页');
      
      return { found: hasSearchContent, bodyPreview: bodyText.substring(0, 200) };
    });
    
    console.log('Search results check:', hasSearchResults);
    
    if (finalUrl.includes('search') || finalUrl !== 'https://www.bilibili.com/') {
      console.log('✅ Search navigation successful!');
    } else {
      console.log('❌ Search navigation failed - still on homepage');
    }
    
    // Wait for user to see the result
    console.log('⏳ Waiting 10 seconds for manual inspection...');
    await page.waitForTimeout(10000);
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    await browser.close();
  }
}

testBilibiliSearch().catch(console.error);
