# Sentra Auto Browser - 最新增强功能

## 🎯 已实现的增强功能

### 1. 浏览器自动安装和路径配置
- ✅ **自动安装**: 当浏览器不存在时自动安装Playwright Chromium
- ✅ **自定义路径**: 支持使用系统已安装的Chrome/Edge浏览器
- ✅ **路径验证**: 自动验证自定义浏览器路径是否有效
- ✅ **智能回退**: 自定义路径无效时自动回退到默认浏览器

### 2. OpenAI代理支持
- ✅ **自定义API端点**: 支持OpenAI兼容的代理服务
- ✅ **多种代理**: 支持yuanplus.cloud、deepseek、moonshot等
- ✅ **无缝切换**: 只需修改OPENAI_BASE_URL即可切换代理
- ✅ **调试信息**: 显示当前使用的API端点

### 3. 符号兼容性优化
- ✅ **ASCII符号**: 所有emoji替换为ASCII兼容符号
- ✅ **终端兼容**: 在所有Windows终端环境下正常显示
- ✅ **智能检测**: 自动检测终端能力并选择合适的符号

### 4. 配置灵活性
- ✅ **多提供商**: 支持OpenAI、Google、Anthropic
- ✅ **优先级**: OpenAI优先，然后Google，最后Anthropic
- ✅ **环境变量**: 完整的环境变量配置支持
- ✅ **运行时覆盖**: CLI参数可覆盖环境配置

## 📋 配置选项

### LLM配置
```env
# OpenAI (默认)
OPENAI_API_KEY=your_key_here
OPENAI_MODEL=gpt-4o
OPENAI_BASE_URL=https://api.openai.com/v1  # 可修改为代理地址

# Google
GOOGLE_API_KEY=your_key_here
GOOGLE_MODEL=gemini-1.5-flash

# Anthropic
ANTHROPIC_API_KEY=your_key_here
ANTHROPIC_MODEL=claude-3-5-sonnet-20241022

# 通用设置
LLM_TEMPERATURE=0
LLM_MAX_TOKENS=4000
```

### 浏览器配置
```env
# 基本设置
BROWSER_HEADLESS=false
BROWSER_WIDTH=1280
BROWSER_HEIGHT=720
BROWSER_TIMEOUT=30000

# 自定义路径（可选）
BROWSER_EXECUTABLE_PATH=C:\Program Files\Google\Chrome\Application\chrome.exe
BROWSER_USER_DATA_DIR=C:\Users\<USER>\AppData\Local\Google\Chrome\User Data

# 自动安装
BROWSER_AUTO_INSTALL=true
```

## 🚀 使用示例

### 基本使用
```bash
# 使用默认配置
node dist/cli/index.js run "访问百度首页"

# 指定提供商
node dist/cli/index.js run "搜索Node.js" --provider openai --model gpt-4o
```

### 代理服务使用
```bash
# 1. 配置.env文件
OPENAI_API_KEY=your_proxy_key
OPENAI_BASE_URL=https://yuanplus.cloud/v1

# 2. 运行任务
node dist/cli/index.js run "访问GitHub" --provider openai
```

### 自定义浏览器
```bash
# 1. 配置.env文件
BROWSER_EXECUTABLE_PATH=C:\Program Files\Google\Chrome\Application\chrome.exe
BROWSER_AUTO_INSTALL=false

# 2. 运行任务
node dist/cli/index.js run "访问网站"
```

## 🔧 故障排除

### 浏览器问题
1. **浏览器未找到**: 运行 `install-browser.bat` 或 `npx playwright install chromium`
2. **自定义路径无效**: 检查路径是否正确，或设置 `BROWSER_AUTO_INSTALL=true`
3. **权限问题**: 以管理员身份运行

### API问题
1. **OpenAI API失败**: 检查API密钥和网络连接
2. **代理服务问题**: 确认代理地址和密钥正确
3. **配额不足**: 检查API账户余额

### 依赖问题
1. **模块找不到**: 运行 `npm install --legacy-peer-deps`
2. **构建失败**: 运行 `npm run build`
3. **TypeScript错误**: 确保Node.js版本18+

## 🎉 总结

项目现在具备了完整的生产环境部署能力：
- 🔄 **自动化安装**: 浏览器自动安装，无需手动配置
- 🌐 **代理支持**: 完整的OpenAI代理服务支持
- 🖥️ **终端兼容**: 在所有Windows终端下正常显示
- ⚙️ **灵活配置**: 丰富的配置选项满足不同需求
- 📦 **即插即用**: 简化的依赖管理，避免版本冲突

现在您可以轻松地将项目部署到任何Windows环境，并根据需要配置不同的API提供商和浏览器选项！
